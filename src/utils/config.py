"""
配置文件，存储列映射和其他设置
"""

# 列字母对应表 - 使用 pandas 读取时需要转换为对应的列名
COL_MAPPING = {
    'L': '科目名称',      # 假设 L 列对应 "科目名称"
    'N': '部门段替换',    # 假设 N 列对应 "部门段替换"
    'W': '原币借方',      # 假设 W 列对应 "原币借方"
    'X': '原币贷方',      # 假设 X 列对应 "原币贷方"
    'Z': '行说明',        # 假设 Z 列对应 "行说明"
    'AG': '金额（万元）',        # 新增列，按照新需求命名
    'AH': '预算明细项',
    'AI': '运营费用大类',
    'AJ': '部门',
    'AK': '部门类别',
    'AL': '是否预算',
    'AM': '是否可控',
    'AN': '是否一盘货'
}

# 允许的科目名称类型
ALLOWED_L_SUBSTRINGS = ['管理费用', '财务费用', '销售费用', '制造费用']

# 应用版本
APP_VERSION = "1.0.0"

# 应用名称
APP_NAME = "财务数据处理工具"

# 窗口大小
WINDOW_WIDTH = 800
WINDOW_HEIGHT = 600