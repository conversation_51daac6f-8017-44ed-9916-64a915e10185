"""
生成示例Excel文件，用于测试财务数据处理工具
"""
import pandas as pd
import os
import numpy as np
from datetime import datetime

def create_sample_excel():
    """创建示例Excel文件"""
    print("正在创建示例Excel文件...")
    
    # 创建数据框
    data = {
        '科目名称': [
            '管理费用-办公费-办公用品', '管理费用-低值易耗品-办公用具', '管理费用-低值易耗品-消防器材',
            '财务费用', '销售费用-差旅费', '管理费用-差旅费', '制造费用', '管理费用-车辆运输费-办公用车-其他',
            '管理费用-服务费-共享服务费用', '管理费用-地方税费-残疾人保障金', '管理费用-劳务费'
        ],
        '部门段替换': ['总部', '西北分公司', '北京分公司', '华南分公司', '华东分公司', 
                  '华北分公司', '西南分公司', '华中分公司', '总部', '西北分公司', '华北分公司'],
        '原币借方': [1000, 2000, 1500, 5000, 3000, 2500, 4000, 1800, 3500, 2200, 2800],
        '原币贷方': [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0],
        '行说明': [
            '购买办公用品', '采购办公椅和文件柜', '消防器材更新', 
            '银行手续费', '销售人员差旅', '管理人员差旅', '原材料消耗',
            '车辆维修费用', '共享服务费', '残疾人保障金', '劳务费用'
        ]
    }
    
    df = pd.DataFrame(data)
    
    # 创建示例文件目录
    os.makedirs('example_files', exist_ok=True)
    
    # 生成文件名
    timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
    filename = f'example_files/财务数据示例_{timestamp}.xlsx'
    
    # 保存到Excel
    df.to_excel(filename, index=False)
    
    print(f"示例文件已创建: {filename}")
    return filename

if __name__ == "__main__":
    create_sample_excel() 