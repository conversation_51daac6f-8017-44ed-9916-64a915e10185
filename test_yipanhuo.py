#!/usr/bin/env python3
"""
测试一盘货逻辑的脚本
"""
import pandas as pd
import logging
from src.processors.excel_processor import ExcelProcessor

# 设置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def create_yipanhuo_test_data():
    """创建包含一盘货的测试数据"""
    test_data = {
        '科目名称': [
            '管理费用-物流费用',
            '管理费用-差旅费',
            '财务费用'
        ],
        '部门段替换': [
            '济南中心仓',
            '中原区域运营中心',
            '集中核算中心'
        ],
        '原币借方': [15000, 3000, 5000],
        '原币贷方': [0, 0, 0],
        '行说明': [
            '一盘货运输费用',
            '普通出差费用',
            '银行利息费用'
        ]
    }
    
    return pd.DataFrame(test_data)

def main():
    """主函数"""
    logger.info("开始测试一盘货逻辑...")
    
    # 创建测试数据
    test_df = create_yipanhuo_test_data()
    logger.info(f"创建了 {len(test_df)} 行测试数据")
    
    # 保存测试数据到Excel文件
    test_file = 'test_yipanhuo_input.xlsx'
    test_df.to_excel(test_file, index=False)
    logger.info(f"测试数据已保存到 {test_file}")
    
    # 创建处理器并处理数据
    processor = ExcelProcessor(logger)
    
    try:
        success = processor.process_file(test_file, 'test_yipanhuo_output.xlsx')
        if success:
            logger.info("处理成功！")
            
            # 读取并显示结果
            result_df = pd.read_excel('test_yipanhuo_output.xlsx')
            logger.info("处理结果:")
            print("\n" + "="*100)
            
            for index, row in result_df.iterrows():
                print(f"\n行 {index + 1}:")
                print(f"  科目名称: {row['科目名称']}")
                print(f"  部门段替换: {row['部门段替换']}")
                print(f"  行说明: {row['行说明']}")
                print(f"  预算明细项: {row['预算明细项']}")
                print(f"  部门: {row['部门']}")
                print(f"  部门类别: {row['部门类别']}")
                print(f"  是否预算: {row['是否预算']}")
                print(f"  是否可控: {row['是否可控']}")
                print(f"  是否一盘货: {row['是否一盘货']}")
                
                # 验证一盘货逻辑
                has_yipanhuo = '一盘货' in str(row['行说明'])
                expected = 'Y' if has_yipanhuo else 'N'
                actual = row['是否一盘货']
                status = "✓" if expected == actual else "✗"
                print(f"  {status} 一盘货验证: 期望={expected}, 实际={actual}")
            
            print("\n" + "="*100)
            
        else:
            logger.error("处理失败！")
            
    except Exception as e:
        logger.error(f"处理过程中发生错误: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
