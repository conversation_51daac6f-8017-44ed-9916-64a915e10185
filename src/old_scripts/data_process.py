import pandas as pd
import argparse
import os.path

# --- 配置参数处理 ---
parser = argparse.ArgumentParser(description='处理Excel数据文件')
parser.add_argument('input_file', nargs='?', default='./test-all.xlsx', 
                    help='输入Excel文件路径')
args = parser.parse_args()

# 设置输入文件路径
input_excel_file = args.input_file

# 根据输入文件名自动生成输出文件名
file_name, file_ext = os.path.splitext(input_excel_file)
output_excel_file = f"{file_name}-处理过{file_ext}"

# 列字母对应表 - 使用 pandas 读取时需要转换为对应的列名
# 通过打印的实际列名匹配 Excel 中的列位置
# 示例映射（请根据实际 Excel 列位置调整）：
col_mapping = {
    'L': '科目名称',      # 假设 L 列对应 "科目名称" 
    'N': '部门段替换',    # 假设 N 列对应 "部门段替换"
    'W': '原币借方',      # 假设 W 列对应 "原币借方" 
    'X': '原币贷方',      # 假设 X 列对应 "原币贷方"
    'Z': '行说明',        # 假设 Z 列对应 "行说明"
    'AG': '费用（万元）',        # 这些列可能不在当前数据中，需要创建
    'AH': '费用项目',
    'AI': '预算大类',
    'AJ': '负责人',
    'AK': '供应链部门',
    'AL': '是否可控',
    'AM': '一盘货'
}

print(f"开始处理文件: {input_excel_file}")

try:
    # 读取Excel文件
    # 可以添加 sheet_name='你的Sheet名' 如果需要指定工作表
    df = pd.read_excel(input_excel_file)
    print(f"成功读取 {len(df)} 行数据。")
    
    # 打印所有列名，帮助确认实际的列名
    print("文件中的实际列名:")
    print(df.columns.tolist())
    
    # 在这里添加代码来显示前几行数据，帮助了解数据结构
    print("\n数据前5行预览:")
    print(df.head())

    # 创建新列用于后续处理
    for col in ['AG', 'AH', 'AI', 'AJ', 'AK', 'AL', 'AM']:
        if col_mapping[col] not in df.columns:
            df[col_mapping[col]] = None
    
    # --- 第一步：数据清洗和筛选 ---
    # 1.1 删除 N 列为空数据的行
    #     注意：dropna会删除包含NaN值的行。如果空单元格是空字符串''而不是NaN，需要先替换
    original_rows = len(df)
    df.dropna(subset=[col_mapping['N']], inplace=True)
    print(f"根据 {col_mapping['N']} 列删除空值后剩余 {len(df)} 行 (删除了 {original_rows - len(df)} 行)。")

    # 1.2 L列保留包含指定费用类型的行，其余删除
    allowed_L_substrings = ['管理费用', '财务费用', '销售费用', '制造费用']
    original_rows = len(df)
    
    # 创建筛选条件：包含任一指定字段
    filter_condition = df[col_mapping['L']].astype(str).str.contains(allowed_L_substrings[0], na=False)
    for substring in allowed_L_substrings[1:]:
        filter_condition = filter_condition | df[col_mapping['L']].astype(str).str.contains(substring, na=False)
    
    df = df[filter_condition]
    print(f"根据 {col_mapping['L']} 列筛选后剩余 {len(df)} 行 (删除了 {original_rows - len(df)} 行)。")

    if df.empty:
        print("筛选后数据为空，处理终止。")
        exit()

    # --- 第二步：计算 费用金额(万元) (AG列) ---
    # 确保 W 和 X 列是数值类型，非数值转为NaN，然后填充0或者进行错误处理
    df[col_mapping['W']] = pd.to_numeric(df[col_mapping['W']], errors='coerce')
    df[col_mapping['X']] = pd.to_numeric(df[col_mapping['X']], errors='coerce')
    # 如果转换后出现NaN (比如原单元格是文本)，可以选择填充0或保留NaN
    # df[[col_W, col_X]] = df[[col_W, col_X]].fillna(0) # 例如，填充0
    df[col_mapping['AG']] = (df[col_mapping['W']] - df[col_mapping['X']]) / 10000
    print(f"已计算 {col_mapping['AG']} 列。")

    # --- 第三步：处理财务费用 ---
    # 先初始化后面要填充的列，以防它们不存在
    for col in [col_mapping['AH'], col_mapping['AI'], col_mapping['AJ'], col_mapping['AK'], col_mapping['AL']]:
        if col not in df.columns:
            df[col] = None # 或者 pd.NA

    finance_mask = (df[col_mapping['L']] == '财务费用')
    df.loc[finance_mask, col_mapping['AH']] = '财务费用'
    df.loc[finance_mask, col_mapping['AI']] = '财务费用'
    df.loc[finance_mask, col_mapping['AJ']] = '/'
    df.loc[finance_mask, col_mapping['AK']] = '集团'
    df.loc[finance_mask, col_mapping['AL']] = '否'
    print(f"已处理 {col_mapping['L']} 为 '财务费用' 的行。")

    # --- 第四步：AH列判别 (复杂条件) ---
    # 初始化AH列
    if col_mapping['AH'] not in df.columns:
        df[col_mapping['AH']] = None
    
    # 财务费用处理逻辑
    finance_mask = df[col_mapping['L']].str.contains('财务费用', na=False)
    df.loc[finance_mask, col_mapping['AH']] = '财务费用'
    df.loc[finance_mask, col_mapping['AI']] = '财务费用'
    df.loc[finance_mask, col_mapping['AJ']] = '/'
    df.loc[finance_mask, col_mapping['AK']] = '集团'
    df.loc[finance_mask, col_mapping['AL']] = '否'
    
    # 管理费用-安全生产-其他
    mask = df[col_mapping['L']].str.contains('管理费用-安全生产-其他', na=False)
    mask_specific = mask & df[col_mapping['Z']].astype(str).str.contains('安全|消防|防洪|演练', na=False)
    df.loc[mask, col_mapping['AH']] = '消防器材'  # 所有情况都是消防器材
    
    # 管理费用-办公费-办公用品
    mask = df[col_mapping['L']].str.contains('管理费用-办公费-办公用品', na=False)
    mask_tool = mask & df[col_mapping['Z']].astype(str).str.contains('防腐|除锈|标签|标识|仪', na=False)
    mask_furniture = mask & df[col_mapping['Z']].astype(str).str.contains('桶|桌|椅|柜', na=False)
    mask_other = mask & (~mask_tool) & (~mask_furniture)
    df.loc[mask_tool, col_mapping['AH']] = '运营工具'
    df.loc[mask_furniture, col_mapping['AH']] = '办公用具'
    df.loc[mask_other, col_mapping['AH']] = '办公用品'
    
    # 管理费用-办公费-其他行政费
    mask = df[col_mapping['L']].str.contains('管理费用-办公费-其他行政费', na=False)
    mask_monitor = mask & df[col_mapping['Z']].astype(str).str.contains('监控', na=False)
    mask_repair = mask & df[col_mapping['Z']].astype(str).str.contains('除草|除雪|垃圾|修|驱', na=False)
    mask_loading = mask & df[col_mapping['Z']].astype(str).str.contains('装卸', na=False)
    mask_tools = mask & df[col_mapping['Z']].astype(str).str.contains('篷布|电池|除锈|贴纸|标识|标签|不干胶|枕木', na=False)
    mask_office = mask & df[col_mapping['Z']].astype(str).str.contains('打印|投标|标书', na=False)
    mask_other = mask & (~mask_monitor) & (~mask_repair) & (~mask_loading) & (~mask_tools) & (~mask_office)
    df.loc[mask_monitor, col_mapping['AH']] = '仓库监控平台费'
    df.loc[mask_repair, col_mapping['AH']] = '办公行政维修'
    df.loc[mask_loading, col_mapping['AH']] = '装卸费'
    df.loc[mask_tools, col_mapping['AH']] = '运营工具'
    df.loc[mask_office, col_mapping['AH']] = '办公用品'
    df.loc[mask_other, col_mapping['AH']] = '办公行政维修'
    
    # 管理费用-办公费-邮寄费
    mask = df[col_mapping['L']].str.contains('管理费用-办公费-邮寄费', na=False)
    df.loc[mask, col_mapping['AH']] = '邮寄费'
    
    # 财产保险费相关
    insurance_patterns = [
        '管理费用-财产保险费-财产一切险',
        '管理费用-财产保险费-其他财产保险',
        '管理费用-财产保险费-公众责任险',
        '管理费用-劳动保险费'
    ]
    mask = df[col_mapping['L']].str.contains('|'.join(insurance_patterns), na=False)
    df.loc[mask, col_mapping['AH']] = '财产一切险'
    
    # 差旅费
    mask = df[col_mapping['L']].str.contains('管理费用-差旅费', na=False)
    df.loc[mask, col_mapping['AH']] = '差旅费'
    
    # 车辆运输费
    mask = df[col_mapping['L']].str.contains('管理费用-车辆运输费-办公用车-其他', na=False)
    df.loc[mask, col_mapping['AH']] = '其他'
    
    # 地方税费
    mask = df[col_mapping['L']].str.contains('管理费用-地方税费-残疾人保障金', na=False)
    df.loc[mask, col_mapping['AH']] = '人力成本'
    
    # 低值易耗品-办公用具
    mask = df[col_mapping['L']].str.contains('管理费用-低值易耗品-办公用具', na=False)
    mask_furniture = mask & df[col_mapping['Z']].astype(str).str.contains('LOGO|家具|桶|桌|椅|柜', na=False)
    mask_tools = mask & df[col_mapping['Z']].astype(str).str.contains('耗材|篷布|电池|除锈|贴纸|标识|标签|不干胶|枕木', na=False)
    mask_other = mask & (~mask_furniture) & (~mask_tools)
    df.loc[mask_furniture, col_mapping['AH']] = '办公工具'
    df.loc[mask_tools, col_mapping['AH']] = '运营工具'
    df.loc[mask_other, col_mapping['AH']] = '办公用具'
    
    # 劳务费
    mask = df[col_mapping['L']].str.contains('管理费用-劳务费', na=False)
    mask_hr = mask & df[col_mapping['Z']].astype(str).str.contains('君润|佩琪|外包', na=False)
    mask_loading = mask & df[col_mapping['Z']].astype(str).str.contains('挪|整理|装卸', na=False)
    mask_moving = mask & df[col_mapping['Z']].astype(str).str.contains('搬迁', na=False)
    mask_cleanup = mask & df[col_mapping['Z']].astype(str).str.contains('除草', na=False)
    mask_other = mask & (~mask_hr) & (~mask_loading) & (~mask_moving) & (~mask_cleanup)
    df.loc[mask_hr, col_mapping['AH']] = '人力成本'
    df.loc[mask_loading, col_mapping['AH']] = '装卸费'
    df.loc[mask_moving, col_mapping['AH']] = '仓库搬迁费'
    df.loc[mask_cleanup, col_mapping['AH']] = '办公行政维修'
    df.loc[mask_other, col_mapping['AH']] = '人力成本'
    
    # 低值易耗品-消防器材
    mask = df[col_mapping['L']].str.contains('管理费用-低值易耗品-消防器材', na=False)
    df.loc[mask, col_mapping['AH']] = '消防器材'
    
    # 低值易耗品-运营工具
    mask = df[col_mapping['L']].str.contains('管理费用-低值易耗品-运营工具', na=False)
    mask_repair = mask & df[col_mapping['Z']].astype(str).str.contains('草|雪|清理|垃圾|修|驱', na=False)
    mask_other = mask & (~mask_repair)
    df.loc[mask_repair, col_mapping['AH']] = '办公行政维修'
    df.loc[mask_other, col_mapping['AH']] = '运营工具'
    
    # 共享服务费用
    mask = df[col_mapping['L']].str.contains('管理费用-服务费-共享服务费用', na=False)
    df.loc[mask, col_mapping['AH']] = '共享服务费'
    
    # 技术服务费
    mask = df[col_mapping['L']].str.contains('管理费用-服务费-技术服务费', na=False)
    mask_tech = mask & df[col_mapping['Z']].astype(str).str.contains('科技', na=False)
    mask_other = mask & (~mask_tech)
    df.loc[mask_tech, col_mapping['AH']] = '系统服务费'
    df.loc[mask_other, col_mapping['AH']] = '咨询费'
    
    # 售后服务费
    mask = df[col_mapping['L']].str.contains('管理费用-服务费-售后服务费-安装费|管理费用-服务费-售后服务费-维修费', na=False)
    df.loc[mask, col_mapping['AH']] = '办公行政维修'
    
    # 福利费、工资等
    mask = df[col_mapping['L']].str.contains('管理费用-福利费', na=False)
    df.loc[mask, col_mapping['AH']] = '福利费'
    
    mask = df[col_mapping['L']].str.contains('管理费用-工资', na=False)
    df.loc[mask, col_mapping['AH']] = '人力成本'
    
    mask = df[col_mapping['L']].str.contains('管理费用-会议费', na=False)
    df.loc[mask, col_mapping['AH']] = '办公用品'
    
    mask = df[col_mapping['L']].str.contains('管理费用-劳动保护费-工装费', na=False)
    df.loc[mask, col_mapping['AH']] = '工服'
    
    mask = df[col_mapping['L']].str.contains('管理费用-劳动保护费-其他劳保用品', na=False)
    df.loc[mask, col_mapping['AH']] = '劳保费'
    
    mask = df[col_mapping['L']].str.contains('管理费用-聘用费', na=False)
    df.loc[mask, col_mapping['AH']] = '人力成本'
    
    # 管理费用-其他
    mask = df[col_mapping['L']].str.contains('管理费用-其他', na=False)
    mask_litigation = mask & df[col_mapping['Z']].astype(str).str.contains('损益|暂估|法院|诉讼|残', na=False)
    mask_quality = mask & df[col_mapping['Z']].astype(str).str.contains('监造', na=False)
    mask_other = mask & (~mask_litigation) & (~mask_quality)
    df.loc[mask_litigation, col_mapping['AH']] = '诉讼费'
    df.loc[mask_quality, col_mapping['AH']] = '质量检测监造费'
    df.loc[mask_other, col_mapping['AH']] = '其他'
    
    # 设备检测费
    mask = df[col_mapping['L']].str.contains('管理费用-设备检测费-其他', na=False)
    mask_fire = mask & df[col_mapping['Z']].astype(str).str.contains('灭火|消防', na=False)
    mask_other = mask & (~mask_fire)
    df.loc[mask_fire, col_mapping['AH']] = '消防器材'
    df.loc[mask_other, col_mapping['AH']] = '质量检测监造费'
    
    # 社会保险金等
    mask = df[col_mapping['L']].str.contains('管理费用-社会保险金', na=False)
    df.loc[mask, col_mapping['AH']] = '人力成本'
    
    mask = df[col_mapping['L']].str.contains('管理费用-市内交通费', na=False)
    df.loc[mask, col_mapping['AH']] = '交通费'
    
    mask = df[col_mapping['L']].str.contains('管理费用-水电暖管理费', na=False)
    df.loc[mask, col_mapping['AH']] = '水电暖管理费'
    
    # 通讯费相关
    mask = df[col_mapping['L']].str.contains('管理费用-通讯费-网络租金', na=False)
    df.loc[mask, col_mapping['AH']] = '网络租金'
    
    mask = df[col_mapping['L']].str.contains('管理费用-通讯费-系统分摊费', na=False)
    df.loc[mask, col_mapping['AH']] = '系统服务费'
    
    mask = df[col_mapping['L']].str.contains('管理费用-通讯费-移动话费', na=False)
    df.loc[mask, col_mapping['AH']] = '话费'
    
    # 维修费相关
    mask = df[col_mapping['L']].str.contains('管理费用-维修费-办公行政维修', na=False)
    mask_monitor = mask & df[col_mapping['Z']].astype(str).str.contains('监控', na=False)
    mask_tools = mask & df[col_mapping['Z']].astype(str).str.contains('耗材|篷布|电池|除锈|贴纸|标识|标签|不干胶|枕木', na=False)
    mask_other = mask & (~mask_monitor) & (~mask_tools)
    df.loc[mask_monitor, col_mapping['AH']] = '仓库监控平台费'
    df.loc[mask_tools, col_mapping['AH']] = '运营工具'
    df.loc[mask_other, col_mapping['AH']] = '办公行政维修'
    
    # 运营维修抢修
    mask = df[col_mapping['L']].str.contains('管理费用-维修费-运营维修抢修-修理支出-日常维护', na=False)
    mask_monitor = mask & df[col_mapping['Z']].astype(str).str.contains('监控', na=False)
    mask_tools = mask & df[col_mapping['Z']].astype(str).str.contains('耗材|篷布|电池|除锈|贴纸|标识|标签|不干胶|枕木', na=False)
    mask_other = mask & (~mask_monitor) & (~mask_tools)
    df.loc[mask_monitor, col_mapping['AH']] = '仓库监控平台费'
    df.loc[mask_tools, col_mapping['AH']] = '运营工具'
    df.loc[mask_other, col_mapping['AH']] = '办公行政维修'
    
    # 无形资产摊销
    mask = df[col_mapping['L']].str.contains('管理费用-无形资产摊销', na=False)
    df.loc[mask, col_mapping['AH']] = '无形资产'
    
    # 物流费用
    mask = df[col_mapping['L']].str.contains('管理费用-物流费用', na=False)
    mask_loading = mask & df[col_mapping['Z']].astype(str).str.contains('装卸|卸车|装车|吊车|叉车|劳务|人工|搬运', na=False)
    mask_moving = mask & df[col_mapping['Z']].astype(str).str.contains('搬迁|搬仓', na=False)
    mask_delivery = mask & df[col_mapping['Z']].astype(str).str.contains('配送|运费|支线', na=False)
    mask_repair = mask & df[col_mapping['Z']].astype(str).str.contains('草|雪|清理|垃圾|修|驱', na=False)
    mask_other = mask & (~mask_loading) & (~mask_moving) & (~mask_delivery) & (~mask_repair)
    df.loc[mask_loading, col_mapping['AH']] = '装卸费'
    df.loc[mask_moving, col_mapping['AH']] = '仓库搬迁费'
    df.loc[mask_delivery, col_mapping['AH']] = '调剂费'
    df.loc[mask_repair, col_mapping['AH']] = '办公行政维修'
    df.loc[mask_other, col_mapping['AH']] = '装卸费'
    
    # 业务招待费及其他
    mask = df[col_mapping['L']].str.contains('管理费用-业务招待费', na=False)
    df.loc[mask, col_mapping['AH']] = '业务招待费'
    
    mask = df[col_mapping['L']].str.contains('管理费用-长期待摊费用摊销', na=False)
    df.loc[mask, col_mapping['AH']] = '长期待摊费用摊销'
    
    mask = df[col_mapping['L']].str.contains('管理费用-折旧', na=False)
    df.loc[mask, col_mapping['AH']] = '折旧'
    
    mask = df[col_mapping['L']].str.contains('管理费用-职工教育经费', na=False)
    df.loc[mask, col_mapping['AH']] = '人力成本'
    
    # 中介服务费相关
    mask = df[col_mapping['L']].str.contains('管理费用-中介服务费-法律事务费', na=False)
    df.loc[mask, col_mapping['AH']] = '其他'
    
    mask = df[col_mapping['L']].str.contains('管理费用-中介服务费-价格认证费', na=False)
    df.loc[mask, col_mapping['AH']] = '咨询费'
    
    mask = df[col_mapping['L']].str.contains('管理费用-中介服务费-鉴证费用', na=False)
    df.loc[mask, col_mapping['AH']] = '咨询费'
    
    mask = df[col_mapping['L']].str.contains('管理费用-中介服务费-审计', na=False)
    df.loc[mask, col_mapping['AH']] = '审计、评估费'
    
    # 咨询费
    mask = df[col_mapping['L']].str.contains('管理费用-中介服务费-咨询费', na=False)
    mask_audit = mask & df[col_mapping['Z']].astype(str).str.contains('审核', na=False)
    mask_quality = mask & df[col_mapping['Z']].astype(str).str.contains('监造', na=False)
    mask_other = mask & (~mask_audit) & (~mask_quality)
    df.loc[mask_audit, col_mapping['AH']] = '审计、评估费'
    df.loc[mask_quality, col_mapping['AH']] = '质量检测监造费'
    df.loc[mask_other, col_mapping['AH']] = '咨询费'
    
    # 住房公积金
    mask = df[col_mapping['L']].str.contains('管理费用-住房公积金', na=False)
    df.loc[mask, col_mapping['AH']] = '人力成本'
    
    # 租赁费
    mask = df[col_mapping['L']].str.contains('管理费用-租赁费', na=False)
    mask_loading = mask & df[col_mapping['Z']].astype(str).str.contains('吊车|叉车', na=False)
    mask_other = mask & (~mask_loading)
    df.loc[mask_loading, col_mapping['AH']] = '装卸费'
    df.loc[mask_other, col_mapping['AH']] = '房租'
    
    # 销售费用相关
    mask = df[col_mapping['L']].str.contains('销售费用-差旅费', na=False)
    df.loc[mask, col_mapping['AH']] = '差旅费'
    
    mask = df[col_mapping['L']].str.contains('销售费用-水电暖管理费', na=False)
    df.loc[mask, col_mapping['AH']] = '水电暖管理费'
    
    mask = df[col_mapping['L']].str.contains('销售费用-业务招待费', na=False)
    df.loc[mask, col_mapping['AH']] = '业务招待费'
    
    # 销售费用-租赁费
    mask = df[col_mapping['L']].str.contains('销售费用-租赁费', na=False)
    mask_loading = mask & df[col_mapping['Z']].astype(str).str.contains('吊车|叉车', na=False)
    mask_other = mask & (~mask_loading)
    df.loc[mask_loading, col_mapping['AH']] = '装卸费'
    df.loc[mask_other, col_mapping['AH']] = '房租'
    
    # 制造费用相关
    mask = df[col_mapping['L']].str.contains('制造费用-中介服务费-法律事务费', na=False)
    df.loc[mask, col_mapping['AH']] = '诉讼费'

    print(f"已完成 {col_mapping['AH']} 列的条件判别。")

    # --- 第五步：保存结果 ---
    # index=False 表示不将DataFrame的索引写入Excel文件
    df.to_excel(output_excel_file, index=False, engine='openpyxl')
    print(f"处理完成！结果已保存到: {output_excel_file}")

except FileNotFoundError:
    print(f"错误：输入文件 {input_excel_file} 未找到。请检查文件路径和名称。")
except KeyError as e:
    print(f"错误：映射的列名不存在: {e}。请检查列名映射。")
except Exception as e:
    print(f"处理过程中发生错误: {e}")