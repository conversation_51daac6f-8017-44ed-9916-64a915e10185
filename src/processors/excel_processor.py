"""
Excel数据处理器核心模块，包含数据处理逻辑
"""
import pandas as pd
import os
from src.utils.config import COL_MAPPING, ALLOWED_L_SUBSTRINGS
from src.utils.helpers import get_output_filename

class ExcelProcessor:
    """
    Excel数据处理器类，负责处理Excel文件中的财务数据
    """
    
    def __init__(self, logger):
        """
        初始化Excel处理器
        
        参数:
            logger: 日志对象，用于记录处理过程
        """
        self.logger = logger
        self.df = None
        self.input_file = None
        self.output_file = None
        
    def process_file(self, input_file, custom_output_file=None):
        """
        处理Excel文件的主函数
        
        参数:
            input_file: 输入文件路径
            custom_output_file: 自定义输出文件路径（可选）
            
        返回:
            布尔值，表示处理是否成功
        """
        try:
            self.input_file = input_file
            
            # 设置输出文件路径
            if custom_output_file:
                self.output_file = custom_output_file
            else:
                self.output_file = get_output_filename(input_file)
                
            self.logger.info(f"开始处理文件: {self.input_file}")
            
            # 读取Excel文件
            self.df = pd.read_excel(self.input_file)
            self.logger.info(f"成功读取 {len(self.df)} 行数据。")
            
            # 检查必要的列是否存在
            self._validate_columns()
            
            # 创建新列用于后续处理
            self._create_new_columns()
            
            # 数据清洗和筛选
            self._clean_and_filter_data()
            
            if self.df.empty:
                self.logger.warning("筛选后数据为空，处理终止。")
                return False
                
            # 计算费用金额
            self._calculate_expense_amount()
            
            # 处理费用项目映射
            self._process_expense_categories()
            
            # 保存结果
            self.df.to_excel(self.output_file, index=False, engine='openpyxl')
            self.logger.info(f"处理完成！结果已保存到: {self.output_file}")
            
            return True
            
        except FileNotFoundError:
            self.logger.error(f"错误：输入文件 {input_file} 未找到。请检查文件路径和名称。")
            return False
        except KeyError as e:
            self.logger.error(f"错误：映射的列名不存在: {e}。请检查列名映射。")
            return False
        except Exception as e:
            self.logger.error(f"处理过程中发生错误: {e}")
            return False
            
    def _validate_columns(self):
        """验证必要的列是否存在"""
        for col_key in ['L', 'N', 'W', 'X', 'Z']:
            if COL_MAPPING[col_key] not in self.df.columns:
                self.logger.warning(f"警告: 列 '{COL_MAPPING[col_key]}' 不存在。")
                
        self.logger.info("列验证完成")
        
    def _create_new_columns(self):
        """创建新列用于后续处理"""
        for col in ['AG', 'AH', 'AI', 'AJ', 'AK', 'AL', 'AM']:
            if COL_MAPPING[col] not in self.df.columns:
                self.df[COL_MAPPING[col]] = None
                
        self.logger.info("已创建所需的新列")
        
    def _clean_and_filter_data(self):
        """数据清洗和筛选"""
        # 记录原始行数
        original_rows = len(self.df)
        
        # 1.1 删除 N 列为空数据的行
        self.df.dropna(subset=[COL_MAPPING['N']], inplace=True)
        self.logger.info(f"根据 {COL_MAPPING['N']} 列删除空值后剩余 {len(self.df)} 行 (删除了 {original_rows - len(self.df)} 行)。")

        # 1.2 L列保留包含指定费用类型的行，其余删除
        original_rows = len(self.df)
        
        # 创建筛选条件：包含任一指定字段
        filter_condition = self.df[COL_MAPPING['L']].astype(str).str.contains(ALLOWED_L_SUBSTRINGS[0], na=False)
        for substring in ALLOWED_L_SUBSTRINGS[1:]:
            filter_condition = filter_condition | self.df[COL_MAPPING['L']].astype(str).str.contains(substring, na=False)
        
        self.df = self.df[filter_condition]
        self.logger.info(f"根据 {COL_MAPPING['L']} 列筛选后剩余 {len(self.df)} 行 (删除了 {original_rows - len(self.df)} 行)。")
        
    def _calculate_expense_amount(self):
        """计算 费用金额(万元) (AG列)"""
        # 确保 W 和 X 列是数值类型，非数值转为NaN，然后填充0
        self.df[COL_MAPPING['W']] = pd.to_numeric(self.df[COL_MAPPING['W']], errors='coerce')
        self.df[COL_MAPPING['X']] = pd.to_numeric(self.df[COL_MAPPING['X']], errors='coerce')
        # 如果转换后出现NaN (比如原单元格是文本)，填充0
        self.df[[COL_MAPPING['W'], COL_MAPPING['X']]] = self.df[[COL_MAPPING['W'], COL_MAPPING['X']]].fillna(0)
        
        # 计算费用金额
        self.df[COL_MAPPING['AG']] = (self.df[COL_MAPPING['W']] - self.df[COL_MAPPING['X']]) / 10000
        self.logger.info(f"已计算 {COL_MAPPING['AG']} 列。")
        
    def _process_expense_categories(self):
        """处理费用项目映射"""
        # 先初始化后面要填充的列，以防它们不存在
        for col in [COL_MAPPING['AH'], COL_MAPPING['AI'], COL_MAPPING['AJ'], COL_MAPPING['AK'], COL_MAPPING['AL']]:
            if col not in self.df.columns:
                self.df[col] = None # 或者 pd.NA

        # --- 财务费用处理逻辑 ---
        finance_mask = (self.df[COL_MAPPING['L']] == '财务费用')
        self.df.loc[finance_mask, COL_MAPPING['AH']] = '财务费用'
        self.df.loc[finance_mask, COL_MAPPING['AI']] = '财务费用'
        self.df.loc[finance_mask, COL_MAPPING['AJ']] = '/'
        self.df.loc[finance_mask, COL_MAPPING['AK']] = '集团'
        self.df.loc[finance_mask, COL_MAPPING['AL']] = '否'
        self.logger.info(f"已处理 {COL_MAPPING['L']} 为 '财务费用' 的行。")
        
        # --- AH列判别 (复杂条件) ---
        # 初始化AH列
        if COL_MAPPING['AH'] not in self.df.columns:
            self.df[COL_MAPPING['AH']] = None
        
        # 财务费用处理逻辑
        finance_mask = self.df[COL_MAPPING['L']].str.contains('财务费用', na=False)
        self.df.loc[finance_mask, COL_MAPPING['AH']] = '财务费用'
        self.df.loc[finance_mask, COL_MAPPING['AI']] = '财务费用'
        self.df.loc[finance_mask, COL_MAPPING['AJ']] = '/'
        self.df.loc[finance_mask, COL_MAPPING['AK']] = '集团'
        self.df.loc[finance_mask, COL_MAPPING['AL']] = '否'
        
        # 管理费用-安全生产-其他
        mask = self.df[COL_MAPPING['L']].str.contains('管理费用-安全生产-其他', na=False)
        mask_specific = mask & self.df[COL_MAPPING['Z']].astype(str).str.contains('安全|消防|防洪|演练', na=False)
        self.df.loc[mask, COL_MAPPING['AH']] = '消防器材'  # 所有情况都是消防器材
        
        # 管理费用-办公费-办公用品
        mask = self.df[COL_MAPPING['L']].str.contains('管理费用-办公费-办公用品', na=False)
        mask_tool = mask & self.df[COL_MAPPING['Z']].astype(str).str.contains('防腐|除锈|标签|标识|仪', na=False)
        mask_furniture = mask & self.df[COL_MAPPING['Z']].astype(str).str.contains('桶|桌|椅|柜', na=False)
        mask_other = mask & (~mask_tool) & (~mask_furniture)
        self.df.loc[mask_tool, COL_MAPPING['AH']] = '运营工具'
        self.df.loc[mask_furniture, COL_MAPPING['AH']] = '办公用具'
        self.df.loc[mask_other, COL_MAPPING['AH']] = '办公用品'
        
        # 管理费用-办公费-其他行政费
        mask = self.df[COL_MAPPING['L']].str.contains('管理费用-办公费-其他行政费', na=False)
        mask_monitor = mask & self.df[COL_MAPPING['Z']].astype(str).str.contains('监控', na=False)
        mask_repair = mask & self.df[COL_MAPPING['Z']].astype(str).str.contains('除草|除雪|垃圾|修|驱', na=False)
        mask_loading = mask & self.df[COL_MAPPING['Z']].astype(str).str.contains('装卸', na=False)
        mask_tools = mask & self.df[COL_MAPPING['Z']].astype(str).str.contains('篷布|电池|除锈|贴纸|标识|标签|不干胶|枕木', na=False)
        mask_office = mask & self.df[COL_MAPPING['Z']].astype(str).str.contains('打印|投标|标书', na=False)
        mask_other = mask & (~mask_monitor) & (~mask_repair) & (~mask_loading) & (~mask_tools) & (~mask_office)
        self.df.loc[mask_monitor, COL_MAPPING['AH']] = '仓库监控平台费'
        self.df.loc[mask_repair, COL_MAPPING['AH']] = '办公行政维修'
        self.df.loc[mask_loading, COL_MAPPING['AH']] = '装卸费'
        self.df.loc[mask_tools, COL_MAPPING['AH']] = '运营工具'
        self.df.loc[mask_office, COL_MAPPING['AH']] = '办公用品'
        self.df.loc[mask_other, COL_MAPPING['AH']] = '办公行政维修'
        
        # 管理费用-办公费-邮寄费
        mask = self.df[COL_MAPPING['L']].str.contains('管理费用-办公费-邮寄费', na=False)
        self.df.loc[mask, COL_MAPPING['AH']] = '邮寄费'
        
        # 财产保险费相关
        insurance_patterns = [
            '管理费用-财产保险费-财产一切险',
            '管理费用-财产保险费-其他财产保险',
            '管理费用-财产保险费-公众责任险',
            '管理费用-劳动保险费'
        ]
        mask = self.df[COL_MAPPING['L']].str.contains('|'.join(insurance_patterns), na=False)
        self.df.loc[mask, COL_MAPPING['AH']] = '财产一切险'
        
        # 差旅费
        mask = self.df[COL_MAPPING['L']].str.contains('管理费用-差旅费', na=False)
        self.df.loc[mask, COL_MAPPING['AH']] = '差旅费'
        
        # 车辆运输费
        mask = self.df[COL_MAPPING['L']].str.contains('管理费用-车辆运输费-办公用车-其他', na=False)
        self.df.loc[mask, COL_MAPPING['AH']] = '其他'
        
        # 地方税费
        mask = self.df[COL_MAPPING['L']].str.contains('管理费用-地方税费-残疾人保障金', na=False)
        self.df.loc[mask, COL_MAPPING['AH']] = '人力成本'
        
        # 低值易耗品-办公用具
        mask = self.df[COL_MAPPING['L']].str.contains('管理费用-低值易耗品-办公用具', na=False)
        mask_furniture = mask & self.df[COL_MAPPING['Z']].astype(str).str.contains('LOGO|家具|桶|桌|椅|柜', na=False)
        mask_tools = mask & self.df[COL_MAPPING['Z']].astype(str).str.contains('耗材|篷布|电池|除锈|贴纸|标识|标签|不干胶|枕木', na=False)
        mask_other = mask & (~mask_furniture) & (~mask_tools)
        self.df.loc[mask_furniture, COL_MAPPING['AH']] = '办公工具'
        self.df.loc[mask_tools, COL_MAPPING['AH']] = '运营工具'
        self.df.loc[mask_other, COL_MAPPING['AH']] = '办公用具'
        
        # 劳务费
        mask = self.df[COL_MAPPING['L']].str.contains('管理费用-劳务费', na=False)
        mask_hr = mask & self.df[COL_MAPPING['Z']].astype(str).str.contains('君润|佩琪|外包', na=False)
        mask_loading = mask & self.df[COL_MAPPING['Z']].astype(str).str.contains('挪|整理|装卸', na=False)
        mask_moving = mask & self.df[COL_MAPPING['Z']].astype(str).str.contains('搬迁', na=False)
        mask_cleanup = mask & self.df[COL_MAPPING['Z']].astype(str).str.contains('除草', na=False)
        mask_other = mask & (~mask_hr) & (~mask_loading) & (~mask_moving) & (~mask_cleanup)
        self.df.loc[mask_hr, COL_MAPPING['AH']] = '人力成本'
        self.df.loc[mask_loading, COL_MAPPING['AH']] = '装卸费'
        self.df.loc[mask_moving, COL_MAPPING['AH']] = '仓库搬迁费'
        self.df.loc[mask_cleanup, COL_MAPPING['AH']] = '办公行政维修'
        self.df.loc[mask_other, COL_MAPPING['AH']] = '人力成本'
        
        # 低值易耗品-消防器材
        mask = self.df[COL_MAPPING['L']].str.contains('管理费用-低值易耗品-消防器材', na=False)
        self.df.loc[mask, COL_MAPPING['AH']] = '消防器材'
        
        # 低值易耗品-运营工具
        mask = self.df[COL_MAPPING['L']].str.contains('管理费用-低值易耗品-运营工具', na=False)
        mask_repair = mask & self.df[COL_MAPPING['Z']].astype(str).str.contains('草|雪|清理|垃圾|修|驱', na=False)
        mask_other = mask & (~mask_repair)
        self.df.loc[mask_repair, COL_MAPPING['AH']] = '办公行政维修'
        self.df.loc[mask_other, COL_MAPPING['AH']] = '运营工具'

        # 共享服务费用
        mask = self.df[COL_MAPPING['L']].str.contains('管理费用-服务费-共享服务费用', na=False)
        self.df.loc[mask, COL_MAPPING['AH']] = '共享服务费'
        
        # 技术服务费
        mask = self.df[COL_MAPPING['L']].str.contains('管理费用-服务费-技术服务费', na=False)
        mask_tech = mask & self.df[COL_MAPPING['Z']].astype(str).str.contains('科技', na=False)
        mask_other = mask & (~mask_tech)
        self.df.loc[mask_tech, COL_MAPPING['AH']] = '系统服务费'
        self.df.loc[mask_other, COL_MAPPING['AH']] = '咨询费'
        
        # 售后服务费
        mask = self.df[COL_MAPPING['L']].str.contains('管理费用-服务费-售后服务费-安装费|管理费用-服务费-售后服务费-维修费', na=False)
        self.df.loc[mask, COL_MAPPING['AH']] = '办公行政维修'
        
        # 福利费、工资等
        mask = self.df[COL_MAPPING['L']].str.contains('管理费用-福利费', na=False)
        self.df.loc[mask, COL_MAPPING['AH']] = '福利费'
        
        mask = self.df[COL_MAPPING['L']].str.contains('管理费用-工资', na=False)
        self.df.loc[mask, COL_MAPPING['AH']] = '人力成本'
        
        mask = self.df[COL_MAPPING['L']].str.contains('管理费用-会议费', na=False)
        self.df.loc[mask, COL_MAPPING['AH']] = '办公用品'
        
        mask = self.df[COL_MAPPING['L']].str.contains('管理费用-劳动保护费-工装费', na=False)
        self.df.loc[mask, COL_MAPPING['AH']] = '工服'
        
        mask = self.df[COL_MAPPING['L']].str.contains('管理费用-劳动保护费-其他劳保用品', na=False)
        self.df.loc[mask, COL_MAPPING['AH']] = '劳保费'
        
        mask = self.df[COL_MAPPING['L']].str.contains('管理费用-聘用费', na=False)
        self.df.loc[mask, COL_MAPPING['AH']] = '人力成本'
        
        # 管理费用-其他
        mask = self.df[COL_MAPPING['L']].str.contains('管理费用-其他', na=False)
        mask_litigation = mask & self.df[COL_MAPPING['Z']].astype(str).str.contains('损益|暂估|法院|诉讼|残', na=False)
        mask_quality = mask & self.df[COL_MAPPING['Z']].astype(str).str.contains('监造', na=False)
        mask_other = mask & (~mask_litigation) & (~mask_quality)
        self.df.loc[mask_litigation, COL_MAPPING['AH']] = '诉讼费'
        self.df.loc[mask_quality, COL_MAPPING['AH']] = '质量检测监造费'
        self.df.loc[mask_other, COL_MAPPING['AH']] = '其他'
        
        # 设备检测费
        mask = self.df[COL_MAPPING['L']].str.contains('管理费用-设备检测费-其他', na=False)
        mask_fire = mask & self.df[COL_MAPPING['Z']].astype(str).str.contains('灭火|消防', na=False)
        mask_other = mask & (~mask_fire)
        self.df.loc[mask_fire, COL_MAPPING['AH']] = '消防器材'
        self.df.loc[mask_other, COL_MAPPING['AH']] = '质量检测监造费'
        
        # 社会保险金等
        mask = self.df[COL_MAPPING['L']].str.contains('管理费用-社会保险金', na=False)
        self.df.loc[mask, COL_MAPPING['AH']] = '人力成本'
        
        mask = self.df[COL_MAPPING['L']].str.contains('管理费用-市内交通费', na=False)
        self.df.loc[mask, COL_MAPPING['AH']] = '交通费'
        
        mask = self.df[COL_MAPPING['L']].str.contains('管理费用-水电暖管理费', na=False)
        self.df.loc[mask, COL_MAPPING['AH']] = '水电暖管理费'
        
        # 通讯费相关
        mask = self.df[COL_MAPPING['L']].str.contains('管理费用-通讯费-网络租金', na=False)
        self.df.loc[mask, COL_MAPPING['AH']] = '网络租金'
        
        mask = self.df[COL_MAPPING['L']].str.contains('管理费用-通讯费-系统分摊费', na=False)
        self.df.loc[mask, COL_MAPPING['AH']] = '系统服务费'
        
        mask = self.df[COL_MAPPING['L']].str.contains('管理费用-通讯费-移动话费', na=False)
        self.df.loc[mask, COL_MAPPING['AH']] = '话费'
        
        # 维修费相关
        mask = self.df[COL_MAPPING['L']].str.contains('管理费用-维修费-办公行政维修', na=False)
        mask_monitor = mask & self.df[COL_MAPPING['Z']].astype(str).str.contains('监控', na=False)
        mask_tools = mask & self.df[COL_MAPPING['Z']].astype(str).str.contains('耗材|篷布|电池|除锈|贴纸|标识|标签|不干胶|枕木', na=False)
        mask_other = mask & (~mask_monitor) & (~mask_tools)
        self.df.loc[mask_monitor, COL_MAPPING['AH']] = '仓库监控平台费'
        self.df.loc[mask_tools, COL_MAPPING['AH']] = '运营工具'
        self.df.loc[mask_other, COL_MAPPING['AH']] = '办公行政维修'
        
        # 运营维修抢修
        mask = self.df[COL_MAPPING['L']].str.contains('管理费用-维修费-运营维修抢修-修理支出-日常维护', na=False)
        mask_monitor = mask & self.df[COL_MAPPING['Z']].astype(str).str.contains('监控', na=False)
        mask_tools = mask & self.df[COL_MAPPING['Z']].astype(str).str.contains('耗材|篷布|电池|除锈|贴纸|标识|标签|不干胶|枕木', na=False)
        mask_other = mask & (~mask_monitor) & (~mask_tools)
        self.df.loc[mask_monitor, COL_MAPPING['AH']] = '仓库监控平台费'
        self.df.loc[mask_tools, COL_MAPPING['AH']] = '运营工具'
        self.df.loc[mask_other, COL_MAPPING['AH']] = '办公行政维修'
        
        # 无形资产摊销
        mask = self.df[COL_MAPPING['L']].str.contains('管理费用-无形资产摊销', na=False)
        self.df.loc[mask, COL_MAPPING['AH']] = '无形资产'
        
        # 物流费用
        mask = self.df[COL_MAPPING['L']].str.contains('管理费用-物流费用', na=False)
        mask_loading = mask & self.df[COL_MAPPING['Z']].astype(str).str.contains('装卸|卸车|装车|吊车|叉车|劳务|人工|搬运', na=False)
        mask_moving = mask & self.df[COL_MAPPING['Z']].astype(str).str.contains('搬迁|搬仓', na=False)
        mask_delivery = mask & self.df[COL_MAPPING['Z']].astype(str).str.contains('配送|运费|支线', na=False)
        mask_repair = mask & self.df[COL_MAPPING['Z']].astype(str).str.contains('草|雪|清理|垃圾|修|驱', na=False)
        mask_other = mask & (~mask_loading) & (~mask_moving) & (~mask_delivery) & (~mask_repair)
        self.df.loc[mask_loading, COL_MAPPING['AH']] = '装卸费'
        self.df.loc[mask_moving, COL_MAPPING['AH']] = '仓库搬迁费'
        self.df.loc[mask_delivery, COL_MAPPING['AH']] = '调剂费'
        self.df.loc[mask_repair, COL_MAPPING['AH']] = '办公行政维修'
        self.df.loc[mask_other, COL_MAPPING['AH']] = '装卸费'
        
        # 业务招待费及其他
        mask = self.df[COL_MAPPING['L']].str.contains('管理费用-业务招待费', na=False)
        self.df.loc[mask, COL_MAPPING['AH']] = '业务招待费'
        
        mask = self.df[COL_MAPPING['L']].str.contains('管理费用-长期待摊费用摊销', na=False)
        self.df.loc[mask, COL_MAPPING['AH']] = '长期待摊费用摊销'
        
        mask = self.df[COL_MAPPING['L']].str.contains('管理费用-折旧', na=False)
        self.df.loc[mask, COL_MAPPING['AH']] = '折旧'
        
        mask = self.df[COL_MAPPING['L']].str.contains('管理费用-职工教育经费', na=False)
        self.df.loc[mask, COL_MAPPING['AH']] = '人力成本'
        
        # 中介服务费相关
        mask = self.df[COL_MAPPING['L']].str.contains('管理费用-中介服务费-法律事务费', na=False)
        self.df.loc[mask, COL_MAPPING['AH']] = '其他'
        
        mask = self.df[COL_MAPPING['L']].str.contains('管理费用-中介服务费-价格认证费', na=False)
        self.df.loc[mask, COL_MAPPING['AH']] = '咨询费'
        
        mask = self.df[COL_MAPPING['L']].str.contains('管理费用-中介服务费-鉴证费用', na=False)
        self.df.loc[mask, COL_MAPPING['AH']] = '咨询费'
        
        mask = self.df[COL_MAPPING['L']].str.contains('管理费用-中介服务费-审计', na=False)
        self.df.loc[mask, COL_MAPPING['AH']] = '审计、评估费'
        
        # 咨询费
        mask = self.df[COL_MAPPING['L']].str.contains('管理费用-中介服务费-咨询费', na=False)
        mask_audit = mask & self.df[COL_MAPPING['Z']].astype(str).str.contains('审核', na=False)
        mask_quality = mask & self.df[COL_MAPPING['Z']].astype(str).str.contains('监造', na=False)
        mask_other = mask & (~mask_audit) & (~mask_quality)
        self.df.loc[mask_audit, COL_MAPPING['AH']] = '审计、评估费'
        self.df.loc[mask_quality, COL_MAPPING['AH']] = '质量检测监造费'
        self.df.loc[mask_other, COL_MAPPING['AH']] = '咨询费'
        
        # 住房公积金
        mask = self.df[COL_MAPPING['L']].str.contains('管理费用-住房公积金', na=False)
        self.df.loc[mask, COL_MAPPING['AH']] = '人力成本'
        
        # 租赁费
        mask = self.df[COL_MAPPING['L']].str.contains('管理费用-租赁费', na=False)
        mask_loading = mask & self.df[COL_MAPPING['Z']].astype(str).str.contains('吊车|叉车', na=False)
        mask_other = mask & (~mask_loading)
        self.df.loc[mask_loading, COL_MAPPING['AH']] = '装卸费'
        self.df.loc[mask_other, COL_MAPPING['AH']] = '房租'
        
        # 销售费用相关
        mask = self.df[COL_MAPPING['L']].str.contains('销售费用-差旅费', na=False)
        self.df.loc[mask, COL_MAPPING['AH']] = '差旅费'
        
        mask = self.df[COL_MAPPING['L']].str.contains('销售费用-水电暖管理费', na=False)
        self.df.loc[mask, COL_MAPPING['AH']] = '水电暖管理费'
        
        mask = self.df[COL_MAPPING['L']].str.contains('销售费用-业务招待费', na=False)
        self.df.loc[mask, COL_MAPPING['AH']] = '业务招待费'
        
        # 销售费用-租赁费
        mask = self.df[COL_MAPPING['L']].str.contains('销售费用-租赁费', na=False)
        mask_loading = mask & self.df[COL_MAPPING['Z']].astype(str).str.contains('吊车|叉车', na=False)
        mask_other = mask & (~mask_loading)
        self.df.loc[mask_loading, COL_MAPPING['AH']] = '装卸费'
        self.df.loc[mask_other, COL_MAPPING['AH']] = '房租'
        
        # 制造费用相关
        mask = self.df[COL_MAPPING['L']].str.contains('制造费用-中介服务费-法律事务费', na=False)
        self.df.loc[mask, COL_MAPPING['AH']] = '诉讼费'
        
        self.logger.info(f"已完成 {COL_MAPPING['AH']} 列的条件判别。")
        
    def get_output_file(self):
        """获取输出文件路径"""
        return self.output_file 